// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id       Int    @id @default(autoincrement())
  username String @unique
  password String

  @@map("users")
}

model Template {
  id          Int       @id @default(autoincrement())
  templateId  String    @unique
  name        String
  alias       String?
  description String?
  variables   String    // JSON string
  isActive    Boolean   @default(true)
  
  // Relations
  qrcodes     QRCode[]

  @@map("templates")
}

model QRCode {
  id                Int       @id @default(autoincrement())
  name              String
  sceneStr          String    @unique
  templateId        Int
  templateData      String    // JSON string
  firstNotifyDate   DateTime
  monthlyNotifyDay  Int
  isActive          Boolean   @default(true)
  endDate           DateTime?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  
  // QR Code specific fields
  qrTicket          String?
  qrUrl             String?
  qrImgUrl          String?
  qrGeneratedAt     DateTime?
  
  // Relations
  template          Template    @relation(fields: [templateId], references: [id], onDelete: Cascade)
  subscribers       Subscriber[]

  @@map("qrcodes")
}

model Subscriber {
  id            Int       @id @default(autoincrement())
  openid        String
  qrcodeId      Int
  subscribedAt  DateTime  @default(now())
  isActive      Boolean   @default(true)
  
  // Relations
  qrcode        QRCode           @relation(fields: [qrcodeId], references: [id], onDelete: Cascade)
  notifications NotificationLog[]

  @@unique([openid, qrcodeId])
  @@map("subscribers")
}

model NotificationLog {
  id           Int       @id @default(autoincrement())
  subscriberId Int
  sentAt       DateTime  @default(now())
  status       String
  error        String?
  
  // Relations
  subscriber   Subscriber @relation(fields: [subscriberId], references: [id], onDelete: Cascade)

  @@map("notification_logs")
}
