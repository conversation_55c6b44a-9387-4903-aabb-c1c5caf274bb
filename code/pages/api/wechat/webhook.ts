import type { NextApiRequest, NextApiResponse } from "next";
import { WeChatAPI } from "@/lib/wechat";
import { prisma } from "@/lib/prisma";
import crypto from "crypto";

// 微信服务器验证
function checkSignature(token: string, timestamp: string, nonce: string, signature: string) {
  const arr = [token, timestamp, nonce].sort();
  const str = arr.join("");
  const sha1 = crypto.createHash("sha1").update(str).digest("hex");
  return sha1 === signature;
}

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { signature, timestamp, nonce, echostr } = req.query as Record<string, string>;
  const token = process.env.WECHAT_TOKEN!;

  if (req.method === "GET") {
    // 微信服务器验证
    if (checkSignature(token, timestamp, nonce, signature)) {
      res.status(200).send(echostr);
    } else {
      res.status(401).send("Invalid signature");
    }
    return;
  }

  if (req.method === "POST") {
    // 读取原始 XML
    let xml = "";
    await new Promise<void>((resolve) => {
      req.on("data", (chunk) => {
        xml += chunk;
      });
      req.on("end", () => resolve());
    });

    const data = await WeChatAPI.parseXML(xml);
    const msg = data.xml;

    // 处理事件
    if (msg.MsgType === "event") {
      if (msg.Event === "subscribe" || msg.Event === "SCAN") {
        // 用户扫码关注或已关注扫码
        const openid = msg.FromUserName;
        const sceneStr = msg.EventKey?.replace(/^qrscene_/, "");
        if (sceneStr) {
          // 查找二维码
          const qrcode = await prisma.qRCode.findUnique({ where: { sceneStr } });
          if (qrcode) {
            // 订阅者入库或激活
            await prisma.subscriber.upsert({
              where: { openid },
              update: { isActive: true, qrcodeId: qrcode.id },
              create: { openid, qrcodeId: qrcode.id },
            });
            // 可选：发送欢迎通知
            // ...（此处可扩展调用 WeChatAPI.sendTemplateMessage）
          }
        }
      }
      if (msg.Event === "unsubscribe") {
        // 取消关注，设置订阅者为不活跃
        const openid = msg.FromUserName;
        await prisma.subscriber.updateMany({
          where: { openid },
          data: { isActive: false },
        });
      }
    }

    res.status(200).send("success");
    return;
  }

  res.status(405).send("Method not allowed");
}
