import type { NextApiRequest, NextApiResponse } from "next";
import { prisma } from "@/lib/prisma";
import { WeChatAPI } from "@/lib/wechat";
import { WECHAT_TEMPLATE, generateTemplateData } from "@/lib/config";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }
  const key = req.headers["x-scheduler-key"];
  if (!key || key !== process.env.SCHEDULER_SECRET_KEY) {
    return res.status(401).json({ error: "无权访问" });
  }

  // 查找所有活跃二维码
  const now = new Date();
  const today = now.getDate();
  const todayStr = now.toISOString().slice(0, 10);

  const qrcodes = await prisma.qRCode.findMany({
    where: {
      isActive: true,
      OR: [
        { endDate: null },
        { endDate: { gt: now } },
      ],
    },
    include: {
      subscribers: true,
    },
  });

  let sent = 0, failed = 0, logs: any[] = [];

  for (const qrcode of qrcodes) {
    // 判断是否到达首次提醒日或每月提醒日
    const firstDate = qrcode.firstNotifyDate.toISOString().slice(0, 10);
    const isFirst = todayStr === firstDate;
    const isMonthly = today === qrcode.paymentDay;

    if (!isFirst && !isMonthly) continue;

    // 计算本月还款日期
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();
    const paymentDate = new Date(currentYear, currentMonth, qrcode.paymentDay);

    // 如果还款日已过，使用下个月的还款日
    if (now > paymentDate) {
      paymentDate.setMonth(paymentDate.getMonth() + 1);
    }

    // 格式化还款日期为 YYYY-MM-DD
    const formattedPaymentDate = paymentDate.toISOString().split('T')[0];

    // 生成模板数据
    const templateData = generateTemplateData(
      qrcode.projectName,
      formattedPaymentDate,
      qrcode.amount
    );

    for (const sub of qrcode.subscribers) {
      try {
        // 发送模板消息
        await WeChatAPI.sendTemplateMessage(
          sub.openid,
          WECHAT_TEMPLATE.templateId,
          templateData
        );
        await prisma.notificationLog.create({
          data: {
            subscriberId: sub.id,
            status: "success",
          },
        });
        sent++;
      } catch (e: any) {
        await prisma.notificationLog.create({
          data: {
            subscriberId: sub.id,
            status: "fail",
            error: e.message,
          },
        });
        failed++;
        logs.push({ openid: sub.openid, error: e.message });
      }
    }
  }

  res.status(200).json({ sent, failed, logs });
}
