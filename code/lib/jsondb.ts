import fs from 'fs';
import path from 'path';

// 数据模型接口定义
export interface User {
  id: number;
  username: string;
  password: string;
}

export interface Template {
  id: number;
  templateId: string;
  name: string;
  alias?: string;
  description?: string;
  variables: string;
  isActive: boolean;
}

export interface QRCode {
  id: number;
  name: string;
  sceneStr: string;
  templateId: number;
  templateData: string;
  firstNotifyDate: Date;
  monthlyNotifyDay: number;
  isActive: boolean;
  endDate?: Date;
  createdAt: Date;
  updatedAt: Date;
  // 二维码相关信息
  qrTicket?: string;
  qrUrl?: string;
  qrImgUrl?: string;
  qrGeneratedAt?: Date;
}

export interface Subscriber {
  id: number;
  openid: string;
  qrcodeId: number;
  subscribedAt: Date;
  isActive: boolean;
}

export interface NotificationLog {
  id: number;
  subscriberId: number;
  sentAt: Date;
  status: string;
  error?: string;
}

// 数据库配置
let dbDir = './data'; // 默认数据目录

// 初始化数据库目录
export function initializeDatabase(directory?: string): void {
  if (directory) {
    dbDir = directory;
  }

  // 确保数据目录存在
  if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true });
  }

  // 初始化各个数据文件
  const files = [
    'users.json',
    'templates.json',
    'qrcodes.json',
    'subscribers.json',
    'notificationLogs.json'
  ];

  files.forEach(file => {
    const filePath = path.join(dbDir, file);
    if (!fs.existsSync(filePath)) {
      fs.writeFileSync(filePath, JSON.stringify([]));
    }
  });
}

// 通用CRUD操作
function readData<T>(fileName: string): T[] {
  const filePath = path.join(dbDir, fileName);
  if (!fs.existsSync(filePath)) {
    return [];
  }
  const data = fs.readFileSync(filePath, 'utf8');
  return JSON.parse(data || '[]');
}

function writeData<T>(fileName: string, data: T[]): void {
  const filePath = path.join(dbDir, fileName);
  fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
}

function getNextId<T extends { id: number }>(data: T[]): number {
  if (data.length === 0) {
    return 1;
  }
  return Math.max(...data.map(item => item.id)) + 1;
}

// 日期处理函数
function serializeDate(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(serializeDate);
  }

  if (typeof obj === 'object') {
    if (obj instanceof Date) {
      return obj.toISOString();
    }

    const result: any = {};
    for (const key in obj) {
      result[key] = serializeDate(obj[key]);
    }
    return result;
  }

  return obj;
}

function deserializeDate(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(deserializeDate);
  }

  if (typeof obj === 'object') {
    const result: any = {};
    for (const key in obj) {
      const value = obj[key];
      if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z$/.test(value)) {
        result[key] = new Date(value);
      } else {
        result[key] = deserializeDate(value);
      }
    }
    return result;
  }

  return obj;
}

// 用户模型操作
export const users = {
  findMany: (): User[] => {
    return readData<User>('users.json');
  },

  findUnique: (args: { where: { id?: number, username?: string } }): User | null => {
    const users = readData<User>('users.json');
    return users.find(user =>
      (args.where.id !== undefined && user.id === args.where.id) ||
      (args.where.username !== undefined && user.username === args.where.username)
    ) || null;
  },

  create: (args: { data: Omit<User, 'id'> }): User => {
    const users = readData<User>('users.json');
    const newUser = { ...args.data, id: getNextId(users) };
    users.push(newUser);
    writeData('users.json', users);
    return newUser;
  },

  update: (args: { where: { id: number }, data: Partial<Omit<User, 'id'>> }): User => {
    const users = readData<User>('users.json');
    const index = users.findIndex(user => user.id === args.where.id);
    if (index === -1) {
      throw new Error(`User with id ${args.where.id} not found`);
    }
    const updatedUser = { ...users[index], ...args.data };
    users[index] = updatedUser;
    writeData('users.json', users);
    return updatedUser;
  },

  delete: (args: { where: { id: number } }): User => {
    const users = readData<User>('users.json');
    const index = users.findIndex(user => user.id === args.where.id);
    if (index === -1) {
      throw new Error(`User with id ${args.where.id} not found`);
    }
    const deletedUser = users[index];
    users.splice(index, 1);
    writeData('users.json', users);
    return deletedUser;
  }
};

// 模板模型操作
export const templates = {
  findMany: (args?: { include?: { qrcodes?: boolean } }): Template[] => {
    const templates = readData<Template>('templates.json');

    if (args?.include?.qrcodes) {
      const qrcodes = readData<QRCode>('qrcodes.json');
      return templates.map(template => ({
        ...template,
        qrcodes: qrcodes.filter(qrcode => qrcode.templateId === template.id)
      })) as any;
    }

    return templates;
  },

  findUnique: (args: { where: { id?: number, templateId?: string }, include?: { qrcodes?: boolean } }): Template | null => {
    const templates = readData<Template>('templates.json');
    const template = templates.find(template =>
      (args.where.id !== undefined && template.id === args.where.id) ||
      (args.where.templateId !== undefined && template.templateId === args.where.templateId)
    );

    if (!template) {
      return null;
    }

    if (args.include?.qrcodes) {
      const qrcodes = readData<QRCode>('qrcodes.json');
      return {
        ...template,
        qrcodes: qrcodes.filter(qrcode => qrcode.templateId === template.id)
      } as any;
    }

    return template;
  },

  create: (args: { data: Omit<Template, 'id'> }): Template => {
    const templates = readData<Template>('templates.json');
    const newTemplate = { ...args.data, id: getNextId(templates) };
    templates.push(newTemplate);
    writeData('templates.json', templates);
    return newTemplate;
  },

  update: (args: { where: { id: number }, data: Partial<Omit<Template, 'id'>> }): Template => {
    const templates = readData<Template>('templates.json');
    const index = templates.findIndex(template => template.id === args.where.id);
    if (index === -1) {
      throw new Error(`Template with id ${args.where.id} not found`);
    }
    const updatedTemplate = { ...templates[index], ...args.data };
    templates[index] = updatedTemplate;
    writeData('templates.json', templates);
    return updatedTemplate;
  },

  delete: (args: { where: { id: number } }): Template => {
    const templates = readData<Template>('templates.json');
    const index = templates.findIndex(template => template.id === args.where.id);
    if (index === -1) {
      throw new Error(`Template with id ${args.where.id} not found`);
    }
    const deletedTemplate = templates[index];
    templates.splice(index, 1);
    writeData('templates.json', templates);
    return deletedTemplate;
  },

  upsert: (args: { where: { templateId: string }, create: Omit<Template, 'id'>, update: Partial<Omit<Template, 'id'>> }): Template => {
    const templateress = readData<Template>('templates.json');
    const index = templateress.findIndex(template => template.templateId === args.where.templateId);

    if (index === -1) {
      return templates.create({ data: args.create });
    } else {
      return templates.update({ where: { id: templateress[index].id }, data: args.update });
    }
  }
};

// QRCode模型操作
export const qRCode = {
  findMany: (args?: { include?: { subscribers?: boolean, template?: boolean }, orderBy?: { id: 'asc' | 'desc' } }): QRCode[] => {
    let qrcodes = readData<QRCode>('qrcodes.json').map(qrcode => ({
      ...qrcode,
      firstNotifyDate: new Date(qrcode.firstNotifyDate),
      endDate: qrcode.endDate ? new Date(qrcode.endDate) : undefined,
      createdAt: new Date(qrcode.createdAt),
      updatedAt: new Date(qrcode.updatedAt)
    }));

    // 排序
    if (args?.orderBy?.id) {
      qrcodes = qrcodes.sort((a, b) => {
        return args.orderBy?.id === 'asc' ? a.id - b.id : b.id - a.id;
      });
    }

    // 包含关联数据
    if (args?.include) {
      if (args.include.subscribers) {
        const subscribers = readData<Subscriber>('subscribers.json');
        qrcodes = qrcodes.map(qrcode => ({
          ...qrcode,
          subscribers: subscribers.filter(subscriber => subscriber.qrcodeId === qrcode.id)
        })) as any;
      }

      if (args.include.template) {
        const templatesData = readData<Template>('templates.json');
        qrcodes = qrcodes.map(qrcode => ({
          ...qrcode,
          template: templatesData.find(template => template.id === qrcode.templateId)
        })) as any;
      }
    }

    return qrcodes;
  },

  findUnique: (args: { where: { id?: number, sceneStr?: string }, include?: { subscribers?: boolean, template?: boolean } }): QRCode | null => {
    const qrcodes = readData<QRCode>('qrcodes.json');
    const qrcode = qrcodes.find(qrcode =>
      (args.where.id !== undefined && qrcode.id === args.where.id) ||
      (args.where.sceneStr !== undefined && qrcode.sceneStr === args.where.sceneStr)
    );

    if (!qrcode) {
      return null;
    }

    const result = {
      ...qrcode,
      firstNotifyDate: new Date(qrcode.firstNotifyDate),
      endDate: qrcode.endDate ? new Date(qrcode.endDate) : undefined,
      createdAt: new Date(qrcode.createdAt),
      updatedAt: new Date(qrcode.updatedAt)
    };

    // 包含关联数据
    if (args.include) {
      if (args.include.subscribers) {
        const subscribers = readData<Subscriber>('subscribers.json');
        (result as any).subscribers = subscribers.filter(subscriber => subscriber.qrcodeId === qrcode.id);
      }

      if (args.include.template) {
        const templates = readData<Template>('templates.json');
        (result as any).template = templates.find(template => template.id === qrcode.templateId) || null;
      }
    }

    return result;
  },

  create: (args: { data: Omit<QRCode, 'id' | 'createdAt' | 'updatedAt'> }): QRCode => {
    const qrcodes = readData<QRCode>('qrcodes.json');
    const now = new Date();
    const newQRCode = {
      ...args.data,
      id: getNextId(qrcodes),
      createdAt: now,
      updatedAt: now
    };

    qrcodes.push(serializeDate(newQRCode) as QRCode);
    writeData('qrcodes.json', qrcodes);

    return {
      ...newQRCode,
      firstNotifyDate: new Date(newQRCode.firstNotifyDate),
      endDate: newQRCode.endDate ? new Date(newQRCode.endDate) : undefined
    };
  },

  update: (args: { where: { id: number }, data: Partial<Omit<QRCode, 'id' | 'createdAt' | 'updatedAt'>> }): QRCode => {
    const qrcodes = readData<QRCode>('qrcodes.json');
    const index = qrcodes.findIndex(qrcode => qrcode.id === args.where.id);

    if (index === -1) {
      throw new Error(`QRCode with id ${args.where.id} not found`);
    }

    const updatedQRCode = {
      ...qrcodes[index],
      ...args.data,
      updatedAt: new Date()
    };

    qrcodes[index] = serializeDate(updatedQRCode) as QRCode;
    writeData('qrcodes.json', qrcodes);

    return deserializeDate(updatedQRCode);
  },

  delete: (args: { where: { id: number } }): QRCode => {
    const qrcodes = readData<QRCode>('qrcodes.json');
    const index = qrcodes.findIndex(qrcode => qrcode.id === args.where.id);

    if (index === -1) {
      throw new Error(`QRCode with id ${args.where.id} not found`);
    }

    const deletedQRCode = qrcodes[index];
    qrcodes.splice(index, 1);
    writeData('qrcodes.json', qrcodes);

    // 删除关联的订阅者
    const subscribers = readData<Subscriber>('subscribers.json');
    const remainingSubscribers = subscribers.filter(subscriber => subscriber.qrcodeId !== args.where.id);
    writeData('subscribers.json', remainingSubscribers);

    return deserializeDate(deletedQRCode);
  }
};

// Subscriber模型操作
export const subscriber = {
  findMany: (args?: { include?: { qrcode?: boolean, notifications?: boolean }, orderBy?: { id: 'asc' | 'desc' } }): Subscriber[] => {
    let subscribers = readData<Subscriber>('subscribers.json').map(subscriber => ({
      ...subscriber,
      subscribedAt: new Date(subscriber.subscribedAt)
    }));

    // 排序
    if (args?.orderBy?.id) {
      subscribers = subscribers.sort((a, b) => {
        return args.orderBy?.id === 'asc' ? a.id - b.id : b.id - a.id;
      });
    }

    // 包含关联数据
    if (args?.include) {
      if (args.include.qrcode) {
        const qrcodes = readData<QRCode>('qrcodes.json');
        subscribers = subscribers.map(subscriber => ({
          ...subscriber,
          qrcode: qrcodes.find(qrcode => qrcode.id === subscriber.qrcodeId)
        })) as any;
      }

      if (args.include.notifications) {
        const logs = readData<NotificationLog>('notificationLogs.json');
        subscribers = subscribers.map(subscriber => ({
          ...subscriber,
          notifications: logs.filter(log => log.subscriberId === subscriber.id)
        })) as any;
      }
    }

    return subscribers;
  },

  findUnique: (args: { where: { id?: number, openid?: string }, include?: { qrcode?: boolean, notifications?: boolean } }): Subscriber | null => {
    const subscribers = readData<Subscriber>('subscribers.json');
    const subscriber = subscribers.find(subscriber =>
      (args.where.id !== undefined && subscriber.id === args.where.id) ||
      (args.where.openid !== undefined && subscriber.openid === args.where.openid)
    );

    if (!subscriber) {
      return null;
    }

    const result = {
      ...subscriber,
      subscribedAt: new Date(subscriber.subscribedAt)
    };

    // 包含关联数据
    if (args.include) {
      if (args.include.qrcode) {
        const qrcodes = readData<QRCode>('qrcodes.json');
        (result as any).qrcode = qrcodes.find(qrcode => qrcode.id === subscriber.qrcodeId) || null;
      }

      if (args.include.notifications) {
        const logs = readData<NotificationLog>('notificationLogs.json');
        (result as any).notifications = logs.filter(log => log.subscriberId === subscriber.id);
      }
    }

    return result;
  },

  create: (args: { data: Omit<Subscriber, 'id' | 'subscribedAt'> }): Subscriber => {
    const subscribers = readData<Subscriber>('subscribers.json');
    const now = new Date();
    const newSubscriber = {
      ...args.data,
      id: getNextId(subscribers),
      subscribedAt: now
    };

    subscribers.push(serializeDate(newSubscriber) as Subscriber);
    writeData('subscribers.json', subscribers);

    return {
      ...newSubscriber,
      subscribedAt: new Date(newSubscriber.subscribedAt)
    };
  },

  update: (args: { where: { id: number }, data: Partial<Omit<Subscriber, 'id' | 'subscribedAt'>> }): Subscriber => {
    const subscribers = readData<Subscriber>('subscribers.json');
    const index = subscribers.findIndex(subscriber => subscriber.id === args.where.id);

    if (index === -1) {
      throw new Error(`Subscriber with id ${args.where.id} not found`);
    }

    const updatedSubscriber = {
      ...subscribers[index],
      ...args.data
    };

    subscribers[index] = serializeDate(updatedSubscriber) as Subscriber;
    writeData('subscribers.json', subscribers);

    return deserializeDate(updatedSubscriber);
  },

  updateMany: (args: { where: { openid?: string }, data: Partial<Omit<Subscriber, 'id' | 'subscribedAt'>> }): { count: number } => {
    const subscribers = readData<Subscriber>('subscribers.json');
    let updateCount = 0;

    const updatedSubscribers = subscribers.map(subscriber => {
      if ((args.where.openid !== undefined && subscriber.openid === args.where.openid)) {
        updateCount++;
        return { ...subscriber, ...args.data };
      }
      return subscriber;
    });

    writeData('subscribers.json', updatedSubscribers);
    return { count: updateCount };
  },

  delete: (args: { where: { id: number } }): Subscriber => {
    const subscribers = readData<Subscriber>('subscribers.json');
    const index = subscribers.findIndex(subscriber => subscriber.id === args.where.id);

    if (index === -1) {
      throw new Error(`Subscriber with id ${args.where.id} not found`);
    }

    const deletedSubscriber = subscribers[index];
    subscribers.splice(index, 1);
    writeData('subscribers.json', subscribers);

    // 删除关联的通知日志
    const logs = readData<NotificationLog>('notificationLogs.json');
    const remainingLogs = logs.filter(log => log.subscriberId !== args.where.id);
    writeData('notificationLogs.json', remainingLogs);

    return deserializeDate(deletedSubscriber);
  },

  upsert: (args: { where: { openid: string }, create: Omit<Subscriber, 'id' | 'subscribedAt'>, update: Partial<Omit<Subscriber, 'id' | 'subscribedAt'>> }): Subscriber => {
    const subscribers = readData<Subscriber>('subscribers.json');
    const index = subscribers.findIndex(subscriber => subscriber.openid === args.where.openid);

    if (index === -1) {
      return subscriber.create({ data: args.create });
    } else {
      return subscriber.update({ where: { id: subscribers[index].id }, data: args.update });
    }
  }
};

// NotificationLog模型操作
export const notificationLog = {
  findMany: (args?: { include?: { subscriber?: boolean }, orderBy?: { sentAt: 'asc' | 'desc' }, take?: number }): NotificationLog[] => {
    let logs = readData<NotificationLog>('notificationLogs.json').map(log => ({
      ...log,
      sentAt: new Date(log.sentAt)
    }));

    // 排序
    if (args?.orderBy?.sentAt) {
      logs = logs.sort((a, b) => {
        return args.orderBy?.sentAt === 'asc'
          ? a.sentAt.getTime() - b.sentAt.getTime()
          : b.sentAt.getTime() - a.sentAt.getTime();
      });
    }

    // 包含关联数据
    if (args?.include?.subscriber) {
      const subscribers = readData<Subscriber>('subscribers.json');
      logs = logs.map(log => ({
        ...log,
        subscriber: subscribers.find(subscriber => subscriber.id === log.subscriberId)
      })) as any;

      // 如果需要包含二维码信息
      if (args.include.subscriber) {
        const qrcodes = readData<QRCode>('qrcodes.json');
        logs = logs.map(log => {
          if ((log as any).subscriber) {
            const qrcode = qrcodes.find(qrcode => qrcode.id === (log as any).subscriber.qrcodeId);
            return {
              ...log,
              subscriber: {
                ...(log as any).subscriber,
                qrcode
              }
            };
          }
          return log;
        }) as any;
      }
    }

    // 限制返回数量
    if (args?.take) {
      logs = logs.slice(0, args.take);
    }

    return logs;
  },

  count: (args?: { where?: { status?: string } }): number => {
    const logs = readData<NotificationLog>('notificationLogs.json');

    if (args?.where?.status) {
      return logs.filter(log => log.status === args?.where?.status).length;
    }

    return logs.length;
  },

  create: (args: { data: Omit<NotificationLog, 'id' | 'sentAt'> }): NotificationLog => {
    const logs = readData<NotificationLog>('notificationLogs.json');
    const now = new Date();
    const newLog = {
      ...args.data,
      id: getNextId(logs),
      sentAt: now
    };

    logs.push(serializeDate(newLog) as NotificationLog);
    writeData('notificationLogs.json', logs);

    return {
      ...newLog,
      sentAt: new Date(newLog.sentAt)
    };
  },

  createMany: (args: { data: Omit<NotificationLog, 'id' | 'sentAt'>[] }): { count: number } => {
    const logs = readData<NotificationLog>('notificationLogs.json');
    const now = new Date();
    let nextId = getNextId(logs);

    const newLogs = args.data.map(item => {
      const newLog = {
        ...item,
        id: nextId++,
        sentAt: now
      };
      return serializeDate(newLog);
    });

    logs.push(...newLogs as NotificationLog[]);
    writeData('notificationLogs.json', logs);

    return { count: args.data.length };
  }
};

// 创建Prisma客户端兼容接口
export const prisma = {
  user: users,
  template: templates,
  qRCode: qRCode,
  subscriber: subscriber,
  notificationLog: notificationLog
};
