"use client";

import { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";

interface Subscriber {
  id: number;
  openid: string;
  subscribedAt: string;
  isActive: boolean;
  qrcode: {
    name: string;
  };
  notifications: {
    id: number;
    sentAt: string;
    status: string;
    error?: string;
  }[];
}

export default function SubscriberDetailPage() {
  const router = useRouter();
  const params = useParams();
  const id = params?.id as string;

  const [subscriber, setSubscriber] = useState<Subscriber | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    async function fetchSubscriber() {
      try {
        const token = localStorage.getItem("token");
        const res = await fetch(`/api/subscribers/${id}`, {
          headers: { Authorization: "Bearer " + token },
        });
        if (!res.ok) {
          setError("获取订阅者失败");
          setLoading(false);
          return;
        }
        const data = await res.json();
        setSubscriber(data.subscriber);
      } catch {
        setError("网络错误");
      } finally {
        setLoading(false);
      }
    }
    fetchSubscriber();
  }, [id]);

  const handleToggleActive = async () => {
    if (!subscriber) return;
    setError("");
    setSaving(true);
    try {
      const token = localStorage.getItem("token");
      const res = await fetch(`/api/subscribers/${id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json", Authorization: "Bearer " + token },
        body: JSON.stringify({ isActive: !subscriber.isActive }),
      });
      if (!res.ok) {
        const data = await res.json();
        setError(data.error || "操作失败");
        setSaving(false);
        return;
      }
      setSubscriber({ ...subscriber, isActive: !subscriber.isActive });
    } catch {
      setError("网络错误");
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm("确认删除该订阅者？此操作不可撤销")) return;
    try {
      const token = localStorage.getItem("token");
      const res = await fetch(`/api/subscribers/${id}`, {
        method: "DELETE",
        headers: { Authorization: "Bearer " + token },
      });
      if (!res.ok) {
        const data = await res.json();
        setError(data.error || "删除失败");
        return;
      }
      router.push("/subscribers");
    } catch {
      setError("网络错误");
    }
  };

  if (loading) return <div>加载中...</div>;
  if (error) return <div className="text-red-600 mb-4">{error}</div>;
  if (!subscriber) return null;

  return (
    <main className="max-w-3xl mx-auto p-6 bg-white rounded shadow space-y-4">
      <h2 className="text-2xl font-bold">订阅者详情</h2>
      <div>
        <strong>微信OpenID:</strong> {subscriber.openid}
      </div>
      <div>
        <strong>关联二维码:</strong> {subscriber.qrcode?.name || "-"}
      </div>
      <div>
        <strong>订阅时间:</strong> {new Date(subscriber.subscribedAt).toLocaleString()}
      </div>
      <div>
        <strong>状态:</strong> {subscriber.isActive ? "激活" : "停用"}
      </div>
      <div className="flex space-x-4 mt-4">
        <button
          onClick={handleToggleActive}
          disabled={saving}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
        >
          {saving ? "处理中..." : subscriber.isActive ? "停用" : "激活"}
        </button>
        <button
          onClick={handleDelete}
          className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
        >
          删除
        </button>
      </div>
      <div>
        <h3 className="mt-6 font-semibold">通知历史</h3>
        <table className="min-w-full bg-white rounded shadow mt-2">
          <thead>
            <tr>
              <th className="border px-4 py-2">发送时间</th>
              <th className="border px-4 py-2">状态</th>
              <th className="border px-4 py-2">错误信息</th>
            </tr>
          </thead>
          <tbody>
            {subscriber.notifications.map((log) => (
              <tr key={log.id}>
                <td className="border px-4 py-2">{new Date(log.sentAt).toLocaleString()}</td>
                <td className="border px-4 py-2">{log.status}</td>
                <td className="border px-4 py-2">{log.error || "-"}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </main>
  );
}
