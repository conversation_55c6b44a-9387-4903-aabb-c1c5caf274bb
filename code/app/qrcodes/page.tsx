"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";

interface Template {
  id: number;
  templateId: string;
  name: string;
  alias?: string;
  description?: string;
  variables: string;
  isActive: boolean;
}

interface QRCode {
  id: number;
  name: string;
  sceneStr: string;
  templateId: number;
  template: Template;
  templateData: string;
  firstNotifyDate: string;
  monthlyNotifyDay: number;
  isActive: boolean;
  endDate?: string | null;
  subscribers: any[];
  qrTicket?: string;
  qrUrl?: string;
  qrImgUrl?: string;
  qrGeneratedAt?: string;
}

export default function QRCodesPage() {
  const router = useRouter();
  const [qrcodes, setQRCodes] = useState<QRCode[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    async function fetchQRCodes() {
      try {
        const token = localStorage.getItem("token");
        const res = await fetch("/api/qrcodes", {
          headers: { Authorization: "Bearer " + token },
        });
        if (!res.ok) {
          setError("获取二维码失败");
          setLoading(false);
          return;
        }
        const data = await res.json();
        setQRCodes(data.qrcodes);
      } catch {
        setError("网络错误");
      } finally {
        setLoading(false);
      }
    }
    fetchQRCodes();
  }, []);

  if (loading) return <div>加载中...</div>;
  if (error) return <div className="text-red-600">{error}</div>;

  return (
    <div>
      <h2 className="text-2xl font-bold mb-6">二维码管理</h2>
      <button
        className="mb-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        onClick={() => router.push("/qrcodes/new")}
      >
        新建租金提醒
      </button>
      <table className="min-w-full bg-white rounded shadow">
        <thead>
          <tr>
            <th className="border px-4 py-2">名称</th>
            <th className="border px-4 py-2">项目名称</th>
            <th className="border px-4 py-2">还款日</th>
            <th className="border px-4 py-2">租金</th>
            <th className="border px-4 py-2">订阅者数量</th>
            <th className="border px-4 py-2">状态</th>
            <th className="border px-4 py-2">二维码</th>
            <th className="border px-4 py-2">操作</th>
          </tr>
        </thead>
        <tbody>
          {qrcodes.map((qr) => {
            // 解析模板数据
            let projectName = "";
            let amount = "";
            try {
              if (qr.templateData) {
                const templateData = JSON.parse(qr.templateData);
                if (templateData.thing12 && templateData.thing12.value) {
                  projectName = templateData.thing12.value;
                }
                if (templateData.amount3 && templateData.amount3.value) {
                  amount = templateData.amount3.value;
                }
              }
            } catch (e) {
              console.error("解析模板数据失败", e);
            }

            return (
              <tr key={qr.id} className="hover:bg-gray-100 cursor-pointer" onClick={() => router.push(`/qrcodes/${qr.id}`)}>
                <td className="border px-4 py-2">{qr.name}</td>
                <td className="border px-4 py-2">{projectName}</td>
                <td className="border px-4 py-2">{qr.monthlyNotifyDay}日</td>
                <td className="border px-4 py-2">{amount}</td>
                <td className="border px-4 py-2">{qr.subscribers.length}</td>
                <td className="border px-4 py-2">{qr.isActive ? "激活" : "停用"}</td>
                <td className="border px-4 py-2">
                  {qr.qrImgUrl ? (
                    <Image
                      src={qr.qrImgUrl}
                      alt="二维码"
                      width={60}
                      height={60}
                      className="mx-auto"
                    />
                  ) : (
                    <span className="text-gray-400">未生成</span>
                  )}
                </td>
                <td className="border px-4 py-2 text-blue-600 underline">查看 / 编辑</td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
}
